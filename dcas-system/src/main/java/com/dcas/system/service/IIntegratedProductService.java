package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.domain.entity.IntegratedProduct;
import com.dcas.common.model.query.IntegratedProductQuery;
import com.dcas.common.model.req.CreateIntegrationReq;
import com.dcas.common.model.req.UpdateIntegrationReq;
import com.dcas.common.model.vo.ProductIntegrationVO;
import com.dcas.common.utils.PageResult;

import java.util.List;

/**
 * 集成产品服务接口
 *
 * <AUTHOR>
 */
public interface IIntegratedProductService extends IService<IntegratedProduct> {
    
    /**
     * 分页查询集成产品列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<ProductIntegrationVO> getIntegrationPage(IntegratedProductQuery query);
    
    /**
     * 根据能力分组查询集成产品
     *
     * @return 按能力分组的集成产品列表
     */
    List<ProductIntegrationVO> getIntegrationGroupByCapability();
    
    /**
     * 创建新的产品集成
     *
     * @param request 创建请求
     * @return 集成产品ID
     */
    Long createIntegration(CreateIntegrationReq request);
    
    /**
     * 更新产品集成
     *
     * @param request 更新请求
     * @return 更新结果
     */
    boolean updateIntegration(UpdateIntegrationReq request);
    
    /**
     * 删除产品集成
     *
     * @param ids 集成产品ID数组
     * @return 删除结果
     */
    boolean deleteIntegrations(Long[] ids);
    
    /**
     * 获取集成产品详情
     *
     * @param id 集成产品ID
     * @return 集成产品详情
     */
    ProductIntegrationVO getIntegrationDetail(Long id);
    
    /**
     * 测试集成连接
     *
     * @param id 集成产品ID
     * @return 测试结果
     */
    boolean testConnection(Long id);
    
    /**
     * 启用/禁用集成
     *
     * @param id 集成产品ID
     * @param enabled 是否启用
     * @return 操作结果
     */
    boolean toggleEnabled(Long id, Boolean enabled);
    
    /**
     * 同步集成状态
     *
     * @return 同步结果
     */
    boolean syncIntegrationStatus();
    
    /**
     * 根据API版本记录ID查询集成产品
     *
     * @param releaseId API版本记录ID
     * @return 集成产品
     */
    IntegratedProduct getByReleaseId(Long releaseId);
    
    /**
     * 查询需要同步状态的集成产品
     *
     * @return 集成产品列表
     */
    List<IntegratedProduct> getForStatusSync();
    
    /**
     * 根据状态查询集成产品数量
     *
     * @param status 状态
     * @return 数量
     */
    int countByStatus(String status);
}
