package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.domain.entity.ApiRelease;
import com.dcas.common.mapper.ApiInterfaceMapper;
import com.dcas.common.mapper.ApiReleaseMapper;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.model.vo.ProductCapabilityTreeVO;
import com.dcas.system.service.IApiReleaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * API版本记录服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiReleaseServiceImpl extends ServiceImpl<ApiReleaseMapper, ApiRelease> implements IApiReleaseService {
    private final ApiInterfaceMapper interfaceMapper;
    
    @Override
    public List<ProductCapabilityTreeVO> getProductCapabilityTree() {
        List<ApiRelease> apiReleases = baseMapper.selectApiReleaseList();
        return buildTree(apiReleases);
    }

    @Override
    public List<IntegrationFormFieldVO> getProductsByCapability(Long id) {

        return null;
    }

    private List<ProductCapabilityTreeVO> buildTree(List<ApiRelease> apiReleases) {
        return apiReleases.stream().collect(Collectors.groupingBy(p ->
                BeanUtil.copyProperties(p, ProductCapabilityTreeVO.class))).entrySet().stream().map(e -> {
            ProductCapabilityTreeVO vo = new ProductCapabilityTreeVO();
            ProductCapabilityTreeVO key = e.getKey();
            vo.setCapability(key.getCapability());
            vo.setProducts(e.getValue().stream().map(apiRelease -> {
                ProductCapabilityTreeVO.ProductInfoVO productInfoVO = new ProductCapabilityTreeVO.ProductInfoVO();
                productInfoVO.setId(apiRelease.getId());
                productInfoVO.setProductName(apiRelease.getName());
                return productInfoVO;
            }).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList());
    }
}
