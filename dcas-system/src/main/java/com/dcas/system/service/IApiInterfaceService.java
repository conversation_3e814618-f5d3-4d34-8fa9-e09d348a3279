package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.model.vo.ProductIntegrationVO;

import java.util.List;

/**
 * API接口服务接口
 *
 * <AUTHOR>
 */
public interface IApiInterfaceService extends IService<ApiInterface> {
    
    /**
     * 根据API版本记录ID查询接口列表
     *
     * @param releaseId API版本记录ID
     * @return 接口列表
     */
    List<ApiInterface> getByReleaseId(Long releaseId);
    
    /**
     * 根据API版本记录ID查询接口VO列表
     *
     * @param releaseId API版本记录ID
     * @return 接口VO列表
     */
    List<ProductIntegrationVO.ApiInterfaceVO> getInterfaceVOByReleaseId(Long releaseId);
    
    /**
     * 根据接口名称和版本记录ID查询接口
     *
     * @param apiName 接口名称
     * @param releaseId API版本记录ID
     * @return 接口信息
     */
    ApiInterface getByNameAndReleaseId(String apiName, Long releaseId);
    
    /**
     * 批量保存接口记录
     *
     * @param interfaces 接口列表
     * @return 保存结果
     */
    boolean batchSave(List<ApiInterface> interfaces);
    
    /**
     * 根据版本记录ID删除接口
     *
     * @param releaseId API版本记录ID
     * @return 删除结果
     */
    boolean deleteByReleaseId(Long releaseId);
}
