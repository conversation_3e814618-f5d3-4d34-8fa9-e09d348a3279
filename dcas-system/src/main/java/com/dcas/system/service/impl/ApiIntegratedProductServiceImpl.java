package com.dcas.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.domain.entity.ApiRelease;
import com.dcas.common.domain.entity.ApiIntegratedProduct;
import com.dcas.common.enums.IntegrationStatusEnum;
import com.dcas.common.mapper.ApiIntegratedProductMapper;
import com.dcas.common.model.query.IntegratedProductQuery;
import com.dcas.common.model.req.CreateIntegrationReq;
import com.dcas.common.model.req.UpdateIntegrationReq;
import com.dcas.common.model.vo.ProductIntegrationVO;
import com.dcas.common.utils.PageResult;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.system.service.IApiReleaseService;
import com.dcas.system.service.ApiIntegratedProductService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 集成产品服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiIntegratedProductServiceImpl extends ServiceImpl<ApiIntegratedProductMapper, ApiIntegratedProduct> implements ApiIntegratedProductService {

    private final ApiIntegratedProductMapper apiIntegratedProductMapper;
    private final IApiReleaseService apiReleaseService;
    private final ObjectMapper objectMapper;

    @Override
    public PageResult<ProductIntegrationVO> getIntegrationPage(IntegratedProductQuery query) {
        try (Page<?> page = PageHelper.startPage(query.getCurrentPage(), query.getPageSize())) {
            List<ProductIntegrationVO> list = apiIntegratedProductMapper.selectIntegrationPage(query);
            return PageResult.ofPage(page.getTotal(), list);
        }
    }

    @Override
    public List<ProductIntegrationVO> getIntegrationGroupByCapability() {
        try {
            return apiIntegratedProductMapper.selectIntegrationGroupByCapability();
        } catch (Exception e) {
            log.error("根据能力分组查询集成产品失败", e);
            throw new RuntimeException("查询集成产品失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createIntegration(CreateIntegrationReq request) {
        try {
            // 检查集成名称是否重复
            LambdaQueryWrapper<ApiIntegratedProduct> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ApiIntegratedProduct::getIntegrationName, request.getIntegrationName());
            ApiIntegratedProduct existing = this.getOne(wrapper);
            if (existing != null) {
                throw new RuntimeException("集成名称已存在");
            }

            // 获取API版本记录信息
            ApiRelease apiRelease = apiReleaseService.getById(request.getReleaseId());
            if (apiRelease == null) {
                throw new RuntimeException("API版本记录不存在");
            }

            // 检查是否已经集成过该产品
            ApiIntegratedProduct existingIntegration = apiIntegratedProductMapper.selectByReleaseId(request.getReleaseId());
            if (existingIntegration != null) {
                throw new RuntimeException("该产品已经集成过了");
            }

            // 创建集成产品记录
            ApiIntegratedProduct apiIntegratedProduct = new ApiIntegratedProduct();
            apiIntegratedProduct.setReleaseId(request.getReleaseId());
            apiIntegratedProduct.setIntegrationName(request.getIntegrationName());
            apiIntegratedProduct.setCapability(apiRelease.getCapability());
            apiIntegratedProduct.setProductName(apiRelease.getName());
            apiIntegratedProduct.setCompany(apiRelease.getCompany());
            apiIntegratedProduct.setLogo(apiRelease.getLogo());
            apiIntegratedProduct.setStatus(IntegrationStatusEnum.NORMAL_CONNECTION.getCode());
            apiIntegratedProduct.setCreateBy(SecurityUtils.getUsername());
            apiIntegratedProduct.setCreateTime(new java.util.Date());

            // 转换配置参数为JSON字符串
            if (request.getConfigParams() != null) {
                apiIntegratedProduct.setConfigParams(objectMapper.writeValueAsString(request.getConfigParams()));
            }
            if (request.getAuthParams() != null) {
                apiIntegratedProduct.setAuthParams(objectMapper.writeValueAsString(request.getAuthParams()));
            }

            this.save(apiIntegratedProduct);

            log.info("创建产品集成成功，集成名称: {}, ID: {}", request.getIntegrationName(), apiIntegratedProduct.getId());
            return apiIntegratedProduct.getId();
        } catch (Exception e) {
            log.error("创建产品集成失败", e);
            throw new RuntimeException("创建产品集成失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateIntegration(UpdateIntegrationReq request) {
        try {
            ApiIntegratedProduct apiIntegratedProduct = this.getById(request.getId());
            if (apiIntegratedProduct == null) {
                throw new RuntimeException("集成产品不存在");
            }

            // 检查集成名称是否重复（排除当前记录）
            ApiIntegratedProduct existing = apiIntegratedProductMapper.selectByNameExcludeId(request.getIntegrationName(), request.getId());
            if (existing != null) {
                throw new RuntimeException("集成名称已存在");
            }

            // 更新集成产品信息
            apiIntegratedProduct.setIntegrationName(request.getIntegrationName());
            apiIntegratedProduct.setUpdateBy(SecurityUtils.getUsername());
            apiIntegratedProduct.setUpdateTime(new java.util.Date());

            // 转换配置参数为JSON字符串
            if (request.getConfigParams() != null) {
                apiIntegratedProduct.setConfigParams(objectMapper.writeValueAsString(request.getConfigParams()));
            }
            if (request.getAuthParams() != null) {
                apiIntegratedProduct.setAuthParams(objectMapper.writeValueAsString(request.getAuthParams()));
            }

            boolean result = this.updateById(apiIntegratedProduct);
            log.info("更新产品集成成功，集成名称: {}, ID: {}", request.getIntegrationName(), request.getId());
            return result;
        } catch (Exception e) {
            log.error("更新产品集成失败", e);
            throw new RuntimeException("更新产品集成失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIntegrations(Long[] ids) {
        try {
            boolean result = this.removeByIds(Arrays.asList(ids));
            log.info("删除产品集成成功，删除数量: {}", ids.length);
            return result;
        } catch (Exception e) {
            log.error("删除产品集成失败", e);
            throw new RuntimeException("删除产品集成失败", e);
        }
    }

    @Override
    public ProductIntegrationVO getIntegrationDetail(Long id) {
        try {
            return apiIntegratedProductMapper.selectIntegrationDetail(id);
        } catch (Exception e) {
            log.error("获取集成产品详情失败，id: {}", id, e);
            throw new RuntimeException("获取集成产品详情失败", e);
        }
    }

    @Override
    public boolean testConnection(Long id) {
        try {
            ApiIntegratedProduct apiIntegratedProduct = this.getById(id);
            if (apiIntegratedProduct == null) {
                throw new RuntimeException("集成产品不存在");
            }

            // TODO: 实现连接测试逻辑
            // 1. 根据集成产品的配置参数和授权参数
            // 2. 调用对应的API接口进行连接测试
            // 3. 返回测试结果

            log.info("测试集成连接，集成名称: {}", apiIntegratedProduct.getIntegrationName());
            return true;
        } catch (Exception e) {
            log.error("测试集成连接失败，id: {}", id, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleEnabled(Long id, Boolean enabled) {
        try {
            ApiIntegratedProduct apiIntegratedProduct = this.getById(id);
            if (apiIntegratedProduct == null) {
                throw new RuntimeException("集成产品不存在");
            }

            apiIntegratedProduct.setUpdateBy(SecurityUtils.getUsername());
            apiIntegratedProduct.setUpdateTime(new java.util.Date());

            boolean result = this.updateById(apiIntegratedProduct);
            log.info("切换集成启用状态成功，集成名称: {}, 启用状态: {}", apiIntegratedProduct.getIntegrationName(), enabled);
            return result;
        } catch (Exception e) {
            log.error("切换集成启用状态失败，id: {}, enabled: {}", id, enabled, e);
            throw new RuntimeException("切换集成启用状态失败", e);
        }
    }

    @Override
    public boolean syncIntegrationStatus() {
        try {
            log.info("开始同步集成状态");

            List<ApiIntegratedProduct> products = this.getForStatusSync();
            if (products.isEmpty()) {
                log.info("没有需要同步状态的集成产品");
                return true;
            }

            // TODO: 实现状态同步逻辑
            // 1. 检查每个集成产品对应的知识库配置是否有更新
            // 2. 测试连接状态
            // 3. 更新集成状态

            for (ApiIntegratedProduct product : products) {
                // 模拟状态检查逻辑
                product.setUpdateTime(new java.util.Date());
            }

            // 批量更新状态
            int result = apiIntegratedProductMapper.batchUpdateStatus(products);
            log.info("同步集成状态完成，更新数量: {}", result);
            return true;
        } catch (Exception e) {
            log.error("同步集成状态失败", e);
            return false;
        }
    }

    @Override
    public ApiIntegratedProduct getByReleaseId(Long releaseId) {
        try {
            return apiIntegratedProductMapper.selectByReleaseId(releaseId);
        } catch (Exception e) {
            log.error("根据API版本记录ID查询集成产品失败，releaseId: {}", releaseId, e);
            throw new RuntimeException("查询集成产品失败", e);
        }
    }

    @Override
    public List<ApiIntegratedProduct> getForStatusSync() {
        try {
            return apiIntegratedProductMapper.selectForStatusSync();
        } catch (Exception e) {
            log.error("查询需要同步状态的集成产品失败", e);
            throw new RuntimeException("查询需要同步状态的集成产品失败", e);
        }
    }

    @Override
    public int countByStatus(String status) {
        try {
            return apiIntegratedProductMapper.countByStatus(status);
        } catch (Exception e) {
            log.error("根据状态查询集成产品数量失败，status: {}", status, e);
            throw new RuntimeException("查询集成产品数量失败", e);
        }
    }
}
