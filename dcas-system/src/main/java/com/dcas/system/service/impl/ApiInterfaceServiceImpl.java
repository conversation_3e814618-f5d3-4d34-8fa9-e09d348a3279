package com.dcas.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.mapper.ApiInterfaceMapper;
import com.dcas.common.model.vo.ProductIntegrationVO;
import com.dcas.system.service.IApiInterfaceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * API接口服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiInterfaceServiceImpl extends ServiceImpl<ApiInterfaceMapper, ApiInterface> implements IApiInterfaceService {
    
    private final ApiInterfaceMapper apiInterfaceMapper;
    
    @Override
    public List<ApiInterface> getByReleaseId(Long releaseId) {
        try {
            return apiInterfaceMapper.selectByReleaseId(releaseId);
        } catch (Exception e) {
            log.error("根据API版本记录ID查询接口列表失败，releaseId: {}", releaseId, e);
            throw new RuntimeException("查询接口列表失败", e);
        }
    }
    
    @Override
    public List<ProductIntegrationVO.ApiInterfaceVO> getInterfaceVOByReleaseId(Long releaseId) {
        try {
            return apiInterfaceMapper.selectInterfaceVOByReleaseId(releaseId);
        } catch (Exception e) {
            log.error("根据API版本记录ID查询接口VO列表失败，releaseId: {}", releaseId, e);
            throw new RuntimeException("查询接口VO列表失败", e);
        }
    }
    
    @Override
    public ApiInterface getByNameAndReleaseId(String apiName, Long releaseId) {
        try {
            return apiInterfaceMapper.selectByNameAndReleaseId(apiName, releaseId);
        } catch (Exception e) {
            log.error("根据接口名称和版本记录ID查询接口失败，apiName: {}, releaseId: {}", apiName, releaseId, e);
            throw new RuntimeException("查询接口失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<ApiInterface> interfaces) {
        try {
            if (interfaces == null || interfaces.isEmpty()) {
                return true;
            }
            
            int result = apiInterfaceMapper.batchInsert(interfaces);
            log.info("批量保存接口记录完成，保存数量: {}", result);
            return result > 0;
        } catch (Exception e) {
            log.error("批量保存接口记录失败", e);
            throw new RuntimeException("批量保存接口记录失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByReleaseId(Long releaseId) {
        try {
            int result = apiInterfaceMapper.deleteByReleaseId(releaseId);
            log.info("根据版本记录ID删除接口完成，releaseId: {}, 删除数量: {}", releaseId, result);
            return result >= 0;
        } catch (Exception e) {
            log.error("根据版本记录ID删除接口失败，releaseId: {}", releaseId, e);
            throw new RuntimeException("删除接口失败", e);
        }
    }
}
