<!-- 示例Mapper XML文件内容，展示主要SQL实现 -->

<!-- ApiReleaseMapper.xml 示例 -->
<mapper namespace="com.dcas.common.mapper.ApiReleaseMapper">
    
    <!-- 查询产品能力树形结构 -->
    <select id="selectProductCapabilityTree" resultType="com.dcas.common.model.vo.ProductCapabilityTreeVO">
        SELECT DISTINCT 
            capability AS capabilityId,
            capability AS capabilityName,
            capability AS capabilityDescription
        FROM api_release 
        ORDER BY capability
    </select>
    
    <!-- 根据能力查询产品列表 -->
    <select id="selectProductsByCapability" resultType="com.dcas.common.model.vo.ProductCapabilityTreeVO$ProductInfoVO">
        SELECT 
            ar.id AS releaseId,
            ar.name AS productName,
            ar.company,
            ar.logo,
            ar.auth,
            ar.ability,
            ar.app_tag AS appTag,
            CASE WHEN ip.id IS NOT NULL THEN TRUE ELSE FALSE END AS integrated,
            ip.id AS integrationId
        FROM api_release ar
        LEFT JOIN integrated_product ip ON ar.id = ip.release_id AND ip.enabled = TRUE
        WHERE ar.capability = #{capability}
        ORDER BY ar.company, ar.name
    </select>
    
    <!-- 查询所有不重复的产品能力 -->
    <select id="selectDistinctCapabilities" resultType="java.lang.String">
        SELECT DISTINCT capability 
        FROM api_release 
        ORDER BY capability
    </select>
    
</mapper>

<!-- IntegratedProductMapper.xml 示例 -->
<mapper namespace="com.dcas.common.mapper.IntegratedProductMapper">
    
    <!-- 分页查询集成产品列表 -->
    <select id="selectIntegrationPage" resultType="com.dcas.common.model.vo.ProductIntegrationVO">
        SELECT 
            ip.id,
            ip.integration_name AS integrationName,
            ip.capability,
            ip.product_name AS productName,
            ip.company,
            ip.logo,
            ip.status,
            CASE ip.status
                WHEN 'NORMAL_CONNECTION' THEN '正常连接'
                WHEN 'CONNECTION_ERROR' THEN '连接错误'
                WHEN 'PARAMETER_UPDATED' THEN '参数已更新'
                WHEN 'DELETED_FROM_KNOWLEDGE_BASE' THEN '已从知识库删除'
                ELSE '未知状态'
            END AS statusDescription,
            ip.enabled,
            ip.last_sync_time AS lastSyncTime,
            ip.create_time AS createTime,
            ip.create_by AS createBy,
            ip.error_message AS errorMessage
        FROM integrated_product ip
        <where>
            <if test="query.integrationName != null and query.integrationName != ''">
                AND ip.integration_name LIKE CONCAT('%', #{query.integrationName}, '%')
            </if>
            <if test="query.capability != null and query.capability != ''">
                AND ip.capability = #{query.capability}
            </if>
            <if test="query.productName != null and query.productName != ''">
                AND ip.product_name LIKE CONCAT('%', #{query.productName}, '%')
            </if>
            <if test="query.company != null and query.company != ''">
                AND ip.company LIKE CONCAT('%', #{query.company}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                AND ip.status = #{query.status}
            </if>
            <if test="query.enabled != null">
                AND ip.enabled = #{query.enabled}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                AND ip.create_by = #{query.createBy}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND ip.create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND ip.create_time &lt;= #{query.endTime}
            </if>
        </where>
        ORDER BY ip.create_time DESC
    </select>
    
    <!-- 根据能力分组查询集成产品 -->
    <select id="selectIntegrationGroupByCapability" resultType="com.dcas.common.model.vo.ProductIntegrationVO">
        SELECT 
            ip.id,
            ip.integration_name AS integrationName,
            ip.capability,
            ip.product_name AS productName,
            ip.company,
            ip.logo,
            ip.status,
            ip.enabled,
            ip.last_sync_time AS lastSyncTime
        FROM integrated_product ip
        WHERE ip.enabled = TRUE
        ORDER BY ip.capability, ip.product_name
    </select>
    
    <!-- 查询集成产品详情（包含接口信息） -->
    <select id="selectIntegrationDetail" resultType="com.dcas.common.model.vo.ProductIntegrationVO">
        SELECT 
            ip.id,
            ip.integration_name AS integrationName,
            ip.capability,
            ip.product_name AS productName,
            ip.company,
            ip.logo,
            ip.status,
            CASE ip.status
                WHEN 'NORMAL_CONNECTION' THEN '正常连接'
                WHEN 'CONNECTION_ERROR' THEN '连接错误'
                WHEN 'PARAMETER_UPDATED' THEN '参数已更新'
                WHEN 'DELETED_FROM_KNOWLEDGE_BASE' THEN '已从知识库删除'
                ELSE '未知状态'
            END AS statusDescription,
            ip.enabled,
            ip.last_sync_time AS lastSyncTime,
            ip.create_time AS createTime,
            ip.create_by AS createBy,
            ip.error_message AS errorMessage
        FROM integrated_product ip
        WHERE ip.id = #{id}
    </select>
    
    <!-- 查询需要同步状态的集成产品 -->
    <select id="selectForStatusSync" resultType="com.dcas.common.domain.entity.IntegratedProduct">
        SELECT * FROM integrated_product 
        WHERE enabled = TRUE 
        AND (last_sync_time IS NULL OR last_sync_time &lt; NOW() - INTERVAL '1 hour')
        ORDER BY last_sync_time ASC
    </select>
    
    <!-- 批量更新集成状态 -->
    <update id="batchUpdateStatus">
        <foreach collection="products" item="product" separator=";">
            UPDATE integrated_product 
            SET status = #{product.status},
                last_sync_time = #{product.lastSyncTime},
                error_message = #{product.errorMessage},
                update_time = #{product.updateTime}
            WHERE id = #{product.id}
        </foreach>
    </update>
    
</mapper>

<!-- ApiInterfaceMapper.xml 示例 -->
<mapper namespace="com.dcas.common.mapper.ApiInterfaceMapper">
    
    <!-- 根据API版本记录ID查询接口VO列表 -->
    <select id="selectInterfaceVOByReleaseId" resultType="com.dcas.common.model.vo.ProductIntegrationVO$ApiInterfaceVO">
        SELECT 
            id,
            api_name AS apiName,
            api_method AS apiMethod,
            api_path AS apiPath,
            req_params AS reqParams,
            req_result AS reqResult,
            sort
        FROM api_interface 
        WHERE release_id = #{releaseId}
        ORDER BY sort ASC, id ASC
    </select>
    
    <!-- 批量插入接口记录 -->
    <insert id="batchInsert">
        INSERT INTO api_interface (
            release_id, api_name, api_method, api_path, req_params, req_result, sort,
            create_time, update_time, create_user, update_user
        ) VALUES
        <foreach collection="interfaces" item="item" separator=",">
            (
                #{item.releaseId}, #{item.apiName}, #{item.apiMethod}, #{item.apiPath},
                #{item.reqParams}, #{item.reqResult}, #{item.sort},
                #{item.createTime}, #{item.updateTime}, #{item.createUser}, #{item.updateUser}
            )
        </foreach>
    </insert>
    
</mapper>
