package com.dcas.common.model.other;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/6/10 11:38
 * @since 1.0.0
 */
@Data
public class InParam {
    private List<ColumnParam> columnList;

    @Data
    public static class ColumnParam {
        @ApiModelProperty("字段名")
        private String columnName;

        @ApiModelProperty("字段注释")
        private String columnComment;

        @ApiModelProperty("数据来源:1-终端输入; 2-接口结果")
        private Integer type;

        @ApiModelProperty("具体内容")
        private String value;
    }
}
