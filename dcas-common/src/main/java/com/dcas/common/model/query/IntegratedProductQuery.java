package com.dcas.common.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 集成产品查询参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel("集成产品查询参数")
public class IntegratedProductQuery {
    
    /**
     * 当前页码
     */
    @ApiModelProperty("当前页码")
    private Integer currentPage = 1;
    
    /**
     * 每页数量
     */
    @ApiModelProperty("每页数量")
    private Integer pageSize = 10;
    
    /**
     * 集成名称（模糊查询）
     */
    @ApiModelProperty("集成名称")
    private String integrationName;
    
    /**
     * 产品能力
     */
    @ApiModelProperty("产品能力")
    private String capability;
    
    /**
     * 产品类型名称
     */
    @ApiModelProperty("产品类型名称")
    private String productName;
    
    /**
     * 所属公司
     */
    @ApiModelProperty("所属公司")
    private String company;
    
    /**
     * 集成状态
     */
    @ApiModelProperty("集成状态")
    private String status;
    
    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean enabled;
    
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;
    
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private String startTime;
    
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private String endTime;
}
