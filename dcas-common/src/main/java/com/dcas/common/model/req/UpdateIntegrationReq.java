package com.dcas.common.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 更新集成请求
 *
 * <AUTHOR>
 */
@Data
@ApiModel("更新集成请求")
public class UpdateIntegrationReq {
    
    /**
     * 集成ID
     */
    @ApiModelProperty(value = "集成ID", required = true)
    @NotNull(message = "集成ID不能为空")
    private Long id;
    
    /**
     * 集成名称
     */
    @ApiModelProperty(value = "集成名称", required = true)
    @NotBlank(message = "集成名称不能为空")
    private String integrationName;
    
    /**
     * 配置参数
     */
    @ApiModelProperty("配置参数")
    private Map<String, Object> configParams;
    
    /**
     * 授权参数
     */
    @ApiModelProperty("授权参数")
    private Map<String, Object> authParams;
    
    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean enabled;
    
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}
