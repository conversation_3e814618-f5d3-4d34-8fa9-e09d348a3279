package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 产品能力树形结构VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("产品能力树形结构")
public class ProductCapabilityTreeVO {
    
    /**
     * 能力名称
     */
    @ApiModelProperty("能力名称")
    private String capability;
    
    /**
     * 该能力下的产品列表
     */
    @ApiModelProperty("该能力下的产品列表")
    private List<ProductInfoVO> products;
    
    /**
     * 产品信息VO
     */
    @Data
    @ApiModel("产品信息")
    public static class ProductInfoVO {
        /**
         * 能力ID
         */
        @ApiModelProperty("能力ID")
        private Long id;
        
        /**
         * 产品名称
         */
        @ApiModelProperty("产品名称")
        private String productName;
    }
}
