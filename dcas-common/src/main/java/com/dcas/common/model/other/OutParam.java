package com.dcas.common.model.other;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/6/10 11:39
 * @since 1.0.0
 */
@Data
public class OutParam {
    private List<ColumnParam> columnList;

    @Data
    private static class ColumnParam {
        @ApiModelProperty("返回字段名")
        private String columnName;
        @ApiModelProperty("返回字段名注释")
        private String columnComment;
        @ApiModelProperty("返回字段定位,用/分割")
        private String location;
        @ApiModelProperty("展示字段")
        private String displayName;
        @ApiModelProperty("参数要求：1-结果展示；2-筛选条件")
        private Integer type;
    }
}
