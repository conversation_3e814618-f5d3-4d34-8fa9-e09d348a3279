package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.model.vo.ProductIntegrationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * API接口Mapper接口
 *
 * <AUTHOR>
 */
public interface ApiInterfaceMapper extends BaseMapper<ApiInterface> {
    
    /**
     * 根据API版本记录ID查询接口列表
     *
     * @param releaseId API版本记录ID
     * @return 接口列表
     */
    List<ApiInterface> selectByReleaseId(@Param("releaseId") Long releaseId);
    
    /**
     * 根据API版本记录ID查询接口VO列表
     *
     * @param releaseId API版本记录ID
     * @return 接口VO列表
     */
    List<ProductIntegrationVO.ApiInterfaceVO> selectInterfaceVOByReleaseId(@Param("releaseId") Long releaseId);
    
    /**
     * 根据接口名称和版本记录ID查询接口
     *
     * @param apiName 接口名称
     * @param releaseId API版本记录ID
     * @return 接口信息
     */
    ApiInterface selectByNameAndReleaseId(@Param("apiName") String apiName, @Param("releaseId") Long releaseId);
    
    /**
     * 批量插入接口记录
     *
     * @param interfaces 接口列表
     * @return 插入数量
     */
    int batchInsert(@Param("interfaces") List<ApiInterface> interfaces);
    
    /**
     * 根据版本记录ID删除接口
     *
     * @param releaseId API版本记录ID
     * @return 删除数量
     */
    int deleteByReleaseId(@Param("releaseId") Long releaseId);
}
