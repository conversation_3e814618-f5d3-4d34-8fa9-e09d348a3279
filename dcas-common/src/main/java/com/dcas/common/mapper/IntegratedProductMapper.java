package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.IntegratedProduct;
import com.dcas.common.model.query.IntegratedProductQuery;
import com.dcas.common.model.vo.ProductIntegrationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 集成产品Mapper接口
 *
 * <AUTHOR>
 */
public interface IntegratedProductMapper extends BaseMapper<IntegratedProduct> {
    
    /**
     * 分页查询集成产品列表
     *
     * @param query 查询参数
     * @return 集成产品列表
     */
    List<ProductIntegrationVO> selectIntegrationPage(@Param("query") IntegratedProductQuery query);
    
    /**
     * 根据能力分组查询集成产品
     *
     * @return 按能力分组的集成产品列表
     */
    List<ProductIntegrationVO> selectIntegrationGroupByCapability();
    
    /**
     * 根据API版本记录ID查询集成产品
     *
     * @param releaseId API版本记录ID
     * @return 集成产品
     */
    IntegratedProduct selectByReleaseId(@Param("releaseId") Long releaseId);
    
    /**
     * 查询需要同步状态的集成产品
     *
     * @return 集成产品列表
     */
    List<IntegratedProduct> selectForStatusSync();
    
    /**
     * 批量更新集成状态
     *
     * @param products 集成产品列表
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("products") List<IntegratedProduct> products);
    
    /**
     * 根据状态查询集成产品数量
     *
     * @param status 状态
     * @return 数量
     */
    int countByStatus(@Param("status") String status);
    
    /**
     * 查询集成产品详情（包含接口信息）
     *
     * @param id 集成产品ID
     * @return 集成产品详情
     */
    ProductIntegrationVO selectIntegrationDetail(@Param("id") Long id);
    
    /**
     * 根据集成名称查询（排除指定ID）
     *
     * @param integrationName 集成名称
     * @param excludeId 排除的ID
     * @return 集成产品
     */
    IntegratedProduct selectByNameExcludeId(@Param("integrationName") String integrationName, @Param("excludeId") Long excludeId);
}
