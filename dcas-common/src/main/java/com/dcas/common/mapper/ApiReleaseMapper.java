package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.ApiRelease;
import com.dcas.common.model.vo.ProductCapabilityTreeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * API版本记录Mapper接口
 *
 * <AUTHOR>
 */
public interface ApiReleaseMapper extends BaseMapper<ApiRelease> {
    List<ApiRelease> selectApiReleaseList();
}
