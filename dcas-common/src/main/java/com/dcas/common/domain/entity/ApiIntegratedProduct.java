package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 集成产品实体类
 * 用于存储已集成的第三方产品信息及其状态
 *
 * <AUTHOR>
 */
@Data
@TableName("api_integrated_product")
public class ApiIntegratedProduct {
    
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联API版本记录ID
     */
    @ApiModelProperty("关联API版本记录ID")
    private Long releaseId;
    
    /**
     * 集成名称
     */
    @ApiModelProperty("集成名称")
    private String integrationName;
    
    /**
     * 产品能力
     */
    @ApiModelProperty("产品能力")
    private String capability;
    
    /**
     * 产品类型名称
     */
    @ApiModelProperty("产品类型名称")
    private String productName;
    
    /**
     * 所属公司
     */
    @ApiModelProperty("所属公司")
    private String company;
    
    /**
     * 产品logo
     */
    @ApiModelProperty("产品logo")
    private String logo;
    
    /**
     * 集成状态
     * NORMAL_CONNECTION: 正常连接
     * CONNECTION_ERROR: 连接错误
     * PARAMETER_UPDATED: 参数已更新
     * DELETED_FROM_KNOWLEDGE_BASE: 已从知识库删除
     */
    @ApiModelProperty("集成状态")
    private String status;
    
    /**
     * 配置参数（JSON格式）
     */
    @ApiModelProperty("配置参数")
    private String configParams;
    
    /**
     * 授权参数
     */
    @ApiModelProperty("授权参数")
    private String authParams;
    
    /**
     * 错误信息
     */
    @ApiModelProperty("错误信息")
    private String errorMessage;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Integer version;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;
}
