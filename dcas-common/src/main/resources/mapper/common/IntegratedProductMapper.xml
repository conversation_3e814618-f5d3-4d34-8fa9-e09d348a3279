<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.IntegratedProductMapper">
    <!-- 分页查询集成产品列表 -->
    <select id="selectIntegrationPage" resultType="com.dcas.common.model.vo.ProductIntegrationVO">
        SELECT
        ip.id,
        ip.integration_name AS integrationName,
        ip.capability,
        ip.product_name AS productName,
        ip.company,
        ip.logo,
        ip.status,
        CASE ip.status
        WHEN 'NORMAL_CONNECTION' THEN '正常连接'
        WHEN 'CONNECTION_ERROR' THEN '连接错误'
        WHEN 'PARAMETER_UPDATED' THEN '参数已更新'
        WHEN 'DELETED_FROM_KNOWLEDGE_BASE' THEN '已从知识库删除'
        ELSE '未知状态'
        END AS statusDescription,
        ip.enabled,
        ip.last_sync_time AS lastSyncTime,
        ip.create_time AS createTime,
        ip.create_by AS createBy,
        ip.error_message AS errorMessage
        FROM integrated_product ip
        <where>
            <if test="query.integrationName != null and query.integrationName != ''">
                AND ip.integration_name LIKE CONCAT('%', #{query.integrationName}, '%')
            </if>
            <if test="query.capability != null and query.capability != ''">
                AND ip.capability = #{query.capability}
            </if>
            <if test="query.productName != null and query.productName != ''">
                AND ip.product_name LIKE CONCAT('%', #{query.productName}, '%')
            </if>
            <if test="query.company != null and query.company != ''">
                AND ip.company LIKE CONCAT('%', #{query.company}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                AND ip.status = #{query.status}
            </if>
            <if test="query.enabled != null">
                AND ip.enabled = #{query.enabled}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                AND ip.create_by = #{query.createBy}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND ip.create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND ip.create_time &lt;= #{query.endTime}
            </if>
        </where>
        ORDER BY ip.create_time DESC
    </select>

    <!-- 根据能力分组查询集成产品 -->
    <select id="selectIntegrationGroupByCapability" resultType="com.dcas.common.model.vo.ProductIntegrationVO">
        SELECT
            ip.id,
            ip.integration_name AS integrationName,
            ip.capability,
            ip.product_name AS productName,
            ip.company,
            ip.logo,
            ip.status,
            ip.enabled,
            ip.last_sync_time AS lastSyncTime
        FROM integrated_product ip
        WHERE ip.enabled = TRUE
        ORDER BY ip.capability, ip.product_name
    </select>

    <!-- 查询集成产品详情（包含接口信息） -->
    <select id="selectIntegrationDetail" resultType="com.dcas.common.model.vo.ProductIntegrationVO">
        SELECT
            ip.id,
            ip.integration_name AS integrationName,
            ip.capability,
            ip.product_name AS productName,
            ip.company,
            ip.logo,
            ip.status,
            CASE ip.status
                WHEN 'NORMAL_CONNECTION' THEN '正常连接'
                WHEN 'CONNECTION_ERROR' THEN '连接错误'
                WHEN 'PARAMETER_UPDATED' THEN '参数已更新'
                WHEN 'DELETED_FROM_KNOWLEDGE_BASE' THEN '已从知识库删除'
                ELSE '未知状态'
                END AS statusDescription,
            ip.enabled,
            ip.last_sync_time AS lastSyncTime,
            ip.create_time AS createTime,
            ip.create_by AS createBy,
            ip.error_message AS errorMessage
        FROM integrated_product ip
        WHERE ip.id = #{id}
    </select>

    <!-- 查询需要同步状态的集成产品 -->
    <select id="selectForStatusSync" resultType="com.dcas.common.domain.entity.ApiIntegratedProduct">
        SELECT * FROM integrated_product
        WHERE enabled = TRUE
          AND (last_sync_time IS NULL OR last_sync_time &lt; NOW() - INTERVAL '1 hour')
        ORDER BY last_sync_time ASC
    </select>

    <!-- 批量更新集成状态 -->
    <update id="batchUpdateStatus">
        <foreach collection="products" item="product" separator=";">
            UPDATE integrated_product
            SET status = #{product.status},
            last_sync_time = #{product.lastSyncTime},
            error_message = #{product.errorMessage},
            update_time = #{product.updateTime}
            WHERE id = #{product.id}
        </foreach>
    </update>
</mapper>