-- 示例API接口数据，展示req_params字段的JSON格式
-- 这些数据展示了getParamsByCapability方法如何解析和转换参数

-- 示例1：安全扫描产品的API接口
INSERT INTO api_interface (
    release_id, api_name, api_method, api_path, req_params, req_result, sort,
    create_time, update_time, create_user, update_user
) VALUES (
    1, 
    '启动扫描任务', 
    'POST', 
    '/api/scan/start',
    '{
        "columnList": [
            {
                "columnName": "target_url",
                "columnComment": "目标URL",
                "type": 1,
                "value": ""
            },
            {
                "columnName": "username",
                "columnComment": "用户名",
                "type": 1,
                "value": ""
            },
            {
                "columnName": "password",
                "columnComment": "密码",
                "type": 1,
                "value": ""
            },
            {
                "columnName": "scan_depth",
                "columnComment": "扫描深度",
                "type": 1,
                "value": "3"
            }
        ]
    }',
    '{"status": "success", "taskId": "12345"}',
    1,
    NOW(), NOW(), 'admin', 'admin'
);

-- 示例2：漏洞检测产品的API接口
INSERT INTO api_interface (
    release_id, api_name, api_method, api_path, req_params, req_result, sort,
    create_time, update_time, create_user, update_user
) VALUES (
    1, 
    '获取扫描结果', 
    'GET', 
    '/api/scan/result',
    '{
        "columnList": [
            {
                "columnName": "task_id",
                "columnComment": "任务ID",
                "type": 1,
                "value": ""
            },
            {
                "columnName": "format",
                "columnComment": "结果格式",
                "type": 1,
                "value": "json"
            }
        ]
    }',
    '{"vulnerabilities": [], "summary": {}}',
    2,
    NOW(), NOW(), 'admin', 'admin'
);

-- 示例3：网络监控产品的API接口
INSERT INTO api_interface (
    release_id, api_name, api_method, api_path, req_params, req_result, sort,
    create_time, update_time, create_user, update_user
) VALUES (
    2, 
    '配置监控规则', 
    'POST', 
    '/api/monitor/rule',
    '{
        "columnList": [
            {
                "columnName": "rule_name",
                "columnComment": "规则名称",
                "type": 1,
                "value": ""
            },
            {
                "columnName": "monitor_endpoint",
                "columnComment": "监控端点",
                "type": 1,
                "value": ""
            },
            {
                "columnName": "alert_email",
                "columnComment": "告警邮箱",
                "type": 1,
                "value": ""
            },
            {
                "columnName": "check_interval",
                "columnComment": "检查间隔(秒)",
                "type": 1,
                "value": "300"
            },
            {
                "columnName": "description",
                "columnComment": "规则描述",
                "type": 1,
                "value": ""
            }
        ]
    }',
    '{"ruleId": "rule_001", "status": "active"}',
    1,
    NOW(), NOW(), 'admin', 'admin'
);

-- 示例API版本记录数据
INSERT INTO api_release (
    name, capability, company, logo, auth, ability, app_tag,
    create_time, update_time, create_user, update_user
) VALUES 
(
    '安全扫描器Pro', 
    '安全扫描', 
    '安全科技公司', 
    'https://example.com/logo1.png',
    'Bearer Token',
    '漏洞扫描,安全检测',
    'security',
    NOW(), NOW(), 'admin', 'admin'
),
(
    '网络监控系统', 
    '网络监控', 
    '网络技术公司', 
    'https://example.com/logo2.png',
    'API Key',
    '网络监控,性能分析',
    'network',
    NOW(), NOW(), 'admin', 'admin'
);

-- 查询示例：获取产品ID为1的所有接口参数
-- SELECT ai.api_name, ai.req_params 
-- FROM api_interface ai 
-- WHERE ai.release_id = 1 
-- ORDER BY ai.sort;

-- 预期的getParamsByCapability(1L)方法返回结果示例：
/*
[
    {
        "fieldName": "target_url",
        "fieldLabel": "目标URL", 
        "fieldType": "url",
        "required": true,
        "placeholder": "请输入API地址，如：https://api.example.com",
        "defaultValue": "",
        "description": "来自接口: 启动扫描任务 - 目标URL",
        "sourceType": 1,
        "validationRules": [
            {
                "type": "required",
                "value": "true", 
                "message": "目标URL不能为空"
            },
            {
                "type": "url",
                "value": "true",
                "message": "请输入有效的URL地址"
            }
        ]
    },
    {
        "fieldName": "username",
        "fieldLabel": "用户名",
        "fieldType": "input", 
        "required": true,
        "placeholder": "请输入用户名",
        "defaultValue": "",
        "description": "来自接口: 启动扫描任务 - 用户名",
        "sourceType": 1,
        "validationRules": [
            {
                "type": "required",
                "value": "true",
                "message": "用户名不能为空"
            }
        ]
    },
    {
        "fieldName": "password", 
        "fieldLabel": "密码",
        "fieldType": "password",
        "required": true,
        "placeholder": "请输入密码",
        "defaultValue": "",
        "description": "来自接口: 启动扫描任务 - 密码", 
        "sourceType": 1,
        "validationRules": [
            {
                "type": "required",
                "value": "true",
                "message": "密码不能为空"
            }
        ]
    },
    // ... 其他字段
]
*/
