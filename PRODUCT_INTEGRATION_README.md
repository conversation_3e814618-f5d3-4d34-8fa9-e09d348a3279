# 第三方产品API集成管理系统

## 概述

本系统实现了第三方产品API集成管理功能，包括知识库同步、产品集成管理、状态监控等核心功能。系统遵循现有项目的代码风格和架构模式。

## 核心功能

### 1. 知识库集成
- **云端数据同步**: 从云端知识库拉取最新的产品配置信息
- **本地数据存储**: 将云端数据同步到本地`api_release`和`api_interface`表
- **定时同步**: 每天凌晨2点自动同步知识库数据

### 2. 产品集成管理
- **能力树展示**: 按产品能力分组展示可集成的产品
- **集成创建**: 选择产品并配置参数创建新集成
- **集成管理**: 支持增删改查、启用禁用等操作
- **连接测试**: 测试集成产品的连接状态

### 3. 状态监控
- **状态同步**: 定期检查集成状态并更新
- **状态分类**: 
  - `NORMAL_CONNECTION`: 正常连接
  - `CONNECTION_ERROR`: 连接错误
  - `PARAMETER_UPDATED`: 参数已更新
  - `DELETED_FROM_KNOWLEDGE_BASE`: 已从知识库删除

## 技术架构

### 数据库设计

#### 现有表结构
- `api_release`: 产品类型、公司、logo、授权参数等
- `api_interface`: API接口、方法、路径、请求响应参数等

#### 新增表结构
- `integrated_product`: 集成产品信息及状态跟踪

### 代码结构

```
dcas-common/
├── domain/entity/
│   ├── ApiRelease.java              # API版本记录实体
│   ├── ApiInterface.java            # API接口实体
│   └── IntegratedProduct.java       # 集成产品实体
├── mapper/
│   ├── ApiReleaseMapper.java        # API版本记录Mapper
│   ├── ApiInterfaceMapper.java      # API接口Mapper
│   └── IntegratedProductMapper.java # 集成产品Mapper
├── model/
│   ├── vo/
│   │   ├── ProductCapabilityTreeVO.java    # 产品能力树VO
│   │   └── ProductIntegrationVO.java       # 产品集成VO
│   ├── req/
│   │   ├── CreateIntegrationReq.java       # 创建集成请求
│   │   └── UpdateIntegrationReq.java       # 更新集成请求
│   └── query/
│       └── IntegratedProductQuery.java     # 集成产品查询参数
└── enums/
    └── IntegrationStatusEnum.java           # 集成状态枚举

dcas-system/
└── service/
    ├── IApiReleaseService.java              # API版本记录服务接口
    ├── IApiInterfaceService.java            # API接口服务接口
    ├── IIntegratedProductService.java       # 集成产品服务接口
    └── impl/
        ├── ApiReleaseServiceImpl.java       # API版本记录服务实现
        ├── ApiInterfaceServiceImpl.java     # API接口服务实现
        └── IntegratedProductServiceImpl.java # 集成产品服务实现

dcas-admin/
├── web/controller/system/
│   └── ProductIntegrationController.java   # 产品集成控制器
└── web/core/task/
    └── IntegrationStatusSyncTask.java       # 状态同步定时任务
```

## API接口

### 产品能力管理
- `GET /api/product/integration/capability-tree` - 查询产品能力树
- `GET /api/product/integration/products/{capability}` - 根据能力查询产品
- `GET /api/product/integration/capabilities` - 查询所有产品能力

### 集成管理
- `GET /api/product/integration/list` - 分页查询集成列表
- `GET /api/product/integration/group-by-capability` - 按能力分组查询
- `GET /api/product/integration/{id}` - 获取集成详情
- `POST /api/product/integration` - 创建新集成
- `PUT /api/product/integration` - 更新集成
- `DELETE /api/product/integration/{ids}` - 删除集成

### 操作功能
- `POST /api/product/integration/{id}/test-connection` - 测试连接
- `POST /api/product/integration/{id}/toggle-enabled` - 启用/禁用
- `POST /api/product/integration/sync-status` - 手动同步状态
- `POST /api/product/integration/sync-knowledge-base` - 同步知识库

## 部署说明

### 1. 数据库初始化
```sql
-- 执行 integrated_product_table.sql 创建新表
psql -d your_database -f integrated_product_table.sql
```

### 2. 配置定时任务
系统已配置两个定时任务：
- 集成状态同步：每30分钟执行一次
- 知识库同步：每天凌晨2点执行一次

### 3. 权限配置
需要为相关用户角色添加以下权限：
- `product:integration:view` - 查看集成
- `product:integration:add` - 创建集成
- `product:integration:edit` - 编辑集成
- `product:integration:remove` - 删除集成
- `product:integration:sync` - 同步操作

## 使用流程

### 1. 知识库同步
1. 系统启动后，首先同步云端知识库数据
2. 将产品配置信息存储到`api_release`和`api_interface`表

### 2. 创建集成
1. 访问产品集成管理页面
2. 查看按能力分组的产品树
3. 选择要集成的产品
4. 配置集成参数（配置参数、授权参数等）
5. 创建集成

### 3. 管理集成
1. 在集成列表页面查看所有集成
2. 可以按能力、状态、公司等条件筛选
3. 支持启用/禁用、测试连接、编辑、删除等操作

### 4. 状态监控
1. 系统自动定期检查集成状态
2. 在页面上可以看到各集成的实时状态
3. 支持手动触发状态同步

## 扩展说明

### 1. 添加新的集成状态
在`IntegrationStatusEnum`中添加新的状态枚举值。

### 2. 自定义同步逻辑
在`IntegratedProductServiceImpl.syncIntegrationStatus()`方法中实现具体的状态检查逻辑。

### 3. 扩展API接口
在相应的Service和Controller中添加新的业务方法。

## 注意事项

1. **事务管理**: 所有涉及数据修改的操作都使用了`@Transactional`注解
2. **异常处理**: 统一的异常处理和日志记录
3. **参数验证**: 使用`@Validated`注解进行参数校验
4. **权限控制**: 使用`@Log`注解记录操作日志
5. **代码风格**: 遵循项目现有的代码风格和命名规范

## 测试建议

1. **单元测试**: 为Service层方法编写单元测试
2. **集成测试**: 测试Controller层的API接口
3. **定时任务测试**: 验证定时任务的执行逻辑
4. **数据库测试**: 验证Mapper层的SQL查询

## 后续优化

1. **缓存优化**: 对频繁查询的数据添加Redis缓存
2. **性能优化**: 对大数据量查询进行分页和索引优化
3. **监控告警**: 添加集成状态异常的告警机制
4. **API限流**: 对外部API调用添加限流保护
