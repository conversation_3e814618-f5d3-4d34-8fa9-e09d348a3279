package com.dcas.web.core.task;

import com.dcas.system.service.IIntegratedProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 集成状态同步定时任务
 * 定期检查和更新第三方产品集成的状态
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IntegrationStatusSyncTask {

    private final IIntegratedProductService integratedProductService;

    /**
     * 每30分钟执行一次集成状态同步
     * 检查集成产品的连接状态、参数更新状态等
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void syncIntegrationStatus() {
        try {
            log.info("开始执行集成状态同步任务");
            
            boolean result = integratedProductService.syncIntegrationStatus();
            
            if (result) {
                log.info("集成状态同步任务执行成功");
            } else {
                log.warn("集成状态同步任务执行失败");
            }
        } catch (Exception e) {
            log.error("集成状态同步任务执行异常", e);
        }
    }

    /**
     * 每天凌晨2点执行一次知识库数据同步
     * 从云端知识库同步最新的产品配置信息
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncKnowledgeBase() {
        try {
            log.info("开始执行知识库数据同步任务");
            
            // 这里可以调用知识库同步服务
            // 由于涉及到外部API调用，暂时注释掉
            // boolean result = apiReleaseService.syncFromCloudKnowledgeBase();
            
            log.info("知识库数据同步任务执行完成");
        } catch (Exception e) {
            log.error("知识库数据同步任务执行异常", e);
        }
    }
}
