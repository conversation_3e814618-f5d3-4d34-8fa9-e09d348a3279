package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.enums.LogType;
import com.dcas.common.model.query.IntegratedProductQuery;
import com.dcas.common.model.req.CreateIntegrationReq;
import com.dcas.common.model.req.UpdateIntegrationReq;
import com.dcas.common.model.vo.ProductCapabilityTreeVO;
import com.dcas.common.model.vo.ProductIntegrationVO;
import com.dcas.common.utils.PageResult;
import com.dcas.system.service.IApiReleaseService;
import com.dcas.system.service.IIntegratedProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 第三方产品API集成管理Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/product/integration")
@Api(tags = "第三方产品API集成管理")
@RequiredArgsConstructor
public class ProductIntegrationController {

    private final IIntegratedProductService integratedProductService;
    private final IApiReleaseService apiReleaseService;

    /**
     * 查询产品能力树形结构
     */
    @GetMapping("/capability-tree")
    @ApiOperation("查询产品能力树形结构")
    public R<List<ProductCapabilityTreeVO>> getCapabilityTree() {
        List<ProductCapabilityTreeVO> tree = apiReleaseService.getProductCapabilityTree();
        return R.success(tree);
    }

    /**
     * 根据能力查询产品列表
     */
    @GetMapping("/products/{capability}")
    @ApiOperation("根据能力查询产品列表")
    public R<List<ProductCapabilityTreeVO.ProductInfoVO>> getProductsByCapability(
            @ApiParam("产品能力") @PathVariable("capability") String capability) {
        List<ProductCapabilityTreeVO.ProductInfoVO> products = apiReleaseService.getProductsByCapability(capability);
        return R.success(products);
    }

    /**
     * 分页查询集成产品列表
     */
    @GetMapping("/list")
    @ApiOperation("分页查询集成产品列表")
    public R<PageResult<ProductIntegrationVO>> getIntegrationList(IntegratedProductQuery query) {
        PageResult<ProductIntegrationVO> result = integratedProductService.getIntegrationPage(query);
        return R.success(result);
    }

    /**
     * 根据能力分组查询集成产品
     */
    @GetMapping("/group-by-capability")
    @ApiOperation("根据能力分组查询集成产品")
    public R<List<ProductIntegrationVO>> getIntegrationGroupByCapability() {
        List<ProductIntegrationVO> result = integratedProductService.getIntegrationGroupByCapability();
        return R.success(result);
    }

    /**
     * 获取集成产品详细信息
     */
    @GetMapping("/{id}")
    @ApiOperation("获取集成产品详细信息")
    public R<ProductIntegrationVO> getIntegrationDetail(@ApiParam("集成产品ID") @PathVariable("id") Long id) {
        ProductIntegrationVO detail = integratedProductService.getIntegrationDetail(id);
        return R.success(detail);
    }

    /**
     * 创建新的产品集成
     */
    @Log(title = "创建产品集成", businessType = BusinessType.INSERT, logType = LogType.OPERATE, module = "产品集成管理")
    @PostMapping
    @ApiOperation("创建新的产品集成")
    public R<Long> createIntegration(@Validated @RequestBody CreateIntegrationReq request) {
        Long integrationId = integratedProductService.createIntegration(request);
        return R.success(integrationId);
    }

    /**
     * 更新产品集成
     */
    @Log(title = "更新产品集成", businessType = BusinessType.UPDATE, logType = LogType.OPERATE, module = "产品集成管理")
    @PutMapping
    @ApiOperation("更新产品集成")
    public R<Boolean> updateIntegration(@Validated @RequestBody UpdateIntegrationReq request) {
        boolean result = integratedProductService.updateIntegration(request);
        return R.success(result);
    }

    /**
     * 删除产品集成
     */
    @Log(title = "删除产品集成", businessType = BusinessType.DELETE, logType = LogType.OPERATE, module = "产品集成管理")
    @DeleteMapping("/{ids}")
    @ApiOperation("删除产品集成")
    public R<Boolean> deleteIntegrations(@ApiParam("集成产品ID数组") @PathVariable Long[] ids) {
        boolean result = integratedProductService.deleteIntegrations(ids);
        return R.success(result);
    }

    /**
     * 测试集成连接
     */
    @Log(title = "测试集成连接", businessType = BusinessType.OTHER, logType = LogType.OPERATE, module = "产品集成管理")
    @PostMapping("/{id}/test-connection")
    @ApiOperation("测试集成连接")
    public R<Boolean> testConnection(@ApiParam("集成产品ID") @PathVariable("id") Long id) {
        boolean result = integratedProductService.testConnection(id);
        return result ? R.success(true, "连接测试成功") : R.fail("连接测试失败");
    }

    /**
     * 启用/禁用集成
     */
    @Log(title = "切换集成启用状态", businessType = BusinessType.UPDATE, logType = LogType.OPERATE, module = "产品集成管理")
    @PostMapping("/{id}/toggle-enabled")
    @ApiOperation("启用/禁用集成")
    public R<Boolean> toggleEnabled(@ApiParam("集成产品ID") @PathVariable("id") Long id,
                                    @ApiParam("是否启用") @RequestParam("enabled") Boolean enabled) {
        boolean result = integratedProductService.toggleEnabled(id, enabled);
        return R.success(result);
    }

    /**
     * 手动同步集成状态
     */
    @Log(title = "同步集成状态", businessType = BusinessType.OTHER, logType = LogType.OPERATE, module = "产品集成管理")
    @PostMapping("/sync-status")
    @ApiOperation("手动同步集成状态")
    public R<Boolean> syncIntegrationStatus() {
        boolean result = integratedProductService.syncIntegrationStatus();
        return result ? R.success(true, "状态同步成功") : R.fail("状态同步失败");
    }

    /**
     * 同步云端知识库数据
     */
    @Log(title = "同步云端知识库", businessType = BusinessType.OTHER, logType = LogType.OPERATE, module = "产品集成管理")
    @PostMapping("/sync-knowledge-base")
    @ApiOperation("同步云端知识库数据")
    public R<Boolean> syncKnowledgeBase() {
        boolean result = apiReleaseService.syncFromCloudKnowledgeBase();
        return result ? R.success(true, "知识库同步成功") : R.fail("知识库同步失败");
    }

    /**
     * 查询所有产品能力
     */
    @GetMapping("/capabilities")
    @ApiOperation("查询所有产品能力")
    public R<List<String>> getCapabilities() {
        List<String> capabilities = apiReleaseService.getDistinctCapabilities();
        return R.success(capabilities);
    }

    /**
     * 根据状态查询集成产品数量
     */
    @GetMapping("/count-by-status/{status}")
    @ApiOperation("根据状态查询集成产品数量")
    public R<Integer> countByStatus(@ApiParam("状态") @PathVariable("status") String status) {
        int count = integratedProductService.countByStatus(status);
        return R.success(count);
    }
}
