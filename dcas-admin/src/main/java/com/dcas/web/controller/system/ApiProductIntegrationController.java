package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.enums.LogType;
import com.dcas.common.model.query.IntegratedProductQuery;
import com.dcas.common.model.req.CreateIntegrationReq;
import com.dcas.common.model.req.UpdateIntegrationReq;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.model.vo.ProductCapabilityTreeVO;
import com.dcas.common.model.vo.ProductIntegrationVO;
import com.dcas.common.utils.PageResult;
import com.dcas.system.service.IApiReleaseService;
import com.dcas.system.service.ApiIntegratedProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品对接管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/integration")
@Api(tags = "产品对接管理")
@RequiredArgsConstructor
public class ApiProductIntegrationController {

    private final ApiIntegratedProductService integratedProductService;
    private final IApiReleaseService apiReleaseService;

    /**
     * 查询产品能力树形结构
     */
    @GetMapping("/capability/tree")
    @ApiOperation("查询产品能力树形结构")
    public R<List<ProductCapabilityTreeVO>> getCapabilityTree() {
        List<ProductCapabilityTreeVO> tree = apiReleaseService.getProductCapabilityTree();
        return R.success(tree);
    }

    /**
     * 根据选择产品类型查询对接该产品配置的接口参数
     */
    @GetMapping("/products/{id}")
    @ApiOperation("根据能力查询产品列表")
    public R<List<IntegrationFormFieldVO>> getProductsByCapability(@ApiParam("产品类型id") @PathVariable("id") Long id) {
        return R.success(apiReleaseService.getProductsByCapability(id));
    }

    /**
     * 分页查询集成产品列表
     */
    @GetMapping("/list")
    @ApiOperation("分页查询集成产品列表")
    public R<PageResult<ProductIntegrationVO>> getIntegrationList(IntegratedProductQuery query) {
        PageResult<ProductIntegrationVO> result = integratedProductService.getIntegrationPage(query);
        return R.success(result);
    }

    /**
     * 根据能力分组查询集成产品
     */
    @GetMapping("/group-by-capability")
    @ApiOperation("根据能力分组查询集成产品")
    public R<List<ProductIntegrationVO>> getIntegrationGroupByCapability() {
        List<ProductIntegrationVO> result = integratedProductService.getIntegrationGroupByCapability();
        return R.success(result);
    }

    /**
     * 获取集成产品详细信息
     */
    @GetMapping("/{id}")
    @ApiOperation("获取集成产品详细信息")
    public R<ProductIntegrationVO> getIntegrationDetail(@ApiParam("集成产品ID") @PathVariable("id") Long id) {
        ProductIntegrationVO detail = integratedProductService.getIntegrationDetail(id);
        return R.success(detail);
    }

    /**
     * 创建新的产品集成
     */
    @Log(title = "创建产品集成", businessType = BusinessType.INSERT, logType = LogType.OPERATE, module = "产品集成管理")
    @PostMapping
    @ApiOperation("创建新的产品集成")
    public R<Long> createIntegration(@Validated @RequestBody CreateIntegrationReq request) {
        Long integrationId = integratedProductService.createIntegration(request);
        return R.success(integrationId);
    }

    /**
     * 更新产品集成
     */
    @Log(title = "更新产品集成", businessType = BusinessType.UPDATE, logType = LogType.OPERATE, module = "产品集成管理")
    @PutMapping
    @ApiOperation("更新产品集成")
    public R<Boolean> updateIntegration(@Validated @RequestBody UpdateIntegrationReq request) {
        boolean result = integratedProductService.updateIntegration(request);
        return R.success(result);
    }

    /**
     * 删除产品集成
     */
    @Log(title = "删除产品集成", businessType = BusinessType.DELETE, logType = LogType.OPERATE, module = "产品集成管理")
    @DeleteMapping("/{ids}")
    @ApiOperation("删除产品集成")
    public R<Boolean> deleteIntegrations(@ApiParam("集成产品ID数组") @PathVariable Long[] ids) {
        boolean result = integratedProductService.deleteIntegrations(ids);
        return R.success(result);
    }

}
