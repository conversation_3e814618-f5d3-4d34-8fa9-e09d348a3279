Do $$
BEGIN
BEGIN
ALTER TABLE public.co_progress ADD update_time timestamp(6) NULL;
EXCEPTION
    WHEN duplicate_column THEN RAISE NOTICE 'column update_time already exists in co_progress.';
END;
END;
$$;
COMMENT ON COLUMN public.co_progress.update_time IS '更新时间';
-- =============================================
-- 工作流任务系统相关表结构
-- =============================================

-- 1. 工作流任务主表
CREATE TABLE IF NOT EXISTS workflow_task (
                                             task_id bigserial PRIMARY KEY,
                                             task_name varchar(100) NOT NULL,
    task_description varchar(500),
    status varchar(20) NOT NULL DEFAULT 'PENDING',
    task_type varchar(50),
    priority integer DEFAULT 2,
    create_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by varchar(64) NOT NULL,
    update_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    update_by varchar(64),
    start_time timestamp(6),
    end_time timestamp(6),
    execution_duration bigint,
    current_step integer DEFAULT 0,
    total_steps integer DEFAULT 0,
    progress_percentage integer DEFAULT 0,
    error_message text,
    execution_result text,
    task_config text,
    restartable varchar(1) DEFAULT '1',
    retry_count integer DEFAULT 0,
    max_retries integer DEFAULT 3
    );

-- 添加表注释
COMMENT ON TABLE workflow_task IS '工作流任务表';
COMMENT ON COLUMN workflow_task.task_id IS '任务ID';
COMMENT ON COLUMN workflow_task.task_name IS '任务名称';
COMMENT ON COLUMN workflow_task.task_description IS '任务描述';
COMMENT ON COLUMN workflow_task.status IS '任务状态：PENDING-待执行, RUNNING-执行中, COMPLETED-已完成, FAILED-失败, TERMINATED-已终止';
COMMENT ON COLUMN workflow_task.task_type IS '任务类型';
COMMENT ON COLUMN workflow_task.priority IS '任务优先级：1-低, 2-中, 3-高';
COMMENT ON COLUMN workflow_task.create_time IS '创建时间';
COMMENT ON COLUMN workflow_task.create_by IS '创建人';
COMMENT ON COLUMN workflow_task.update_time IS '更新时间';
COMMENT ON COLUMN workflow_task.update_by IS '更新人';
COMMENT ON COLUMN workflow_task.start_time IS '开始时间';
COMMENT ON COLUMN workflow_task.end_time IS '结束时间';
COMMENT ON COLUMN workflow_task.execution_duration IS '执行时长（毫秒）';
COMMENT ON COLUMN workflow_task.current_step IS '当前执行步骤';
COMMENT ON COLUMN workflow_task.total_steps IS '总步骤数';
COMMENT ON COLUMN workflow_task.progress_percentage IS '执行进度百分比';
COMMENT ON COLUMN workflow_task.error_message IS '错误信息';
COMMENT ON COLUMN workflow_task.execution_result IS '执行结果';
COMMENT ON COLUMN workflow_task.task_config IS '任务配置JSON';
COMMENT ON COLUMN workflow_task.restartable IS '是否可重启：0-否, 1-是';
COMMENT ON COLUMN workflow_task.retry_count IS '重试次数';
COMMENT ON COLUMN workflow_task.max_retries IS '最大重试次数';

-- 2. 任务步骤表
CREATE TABLE IF NOT EXISTS task_step (
                                         step_id bigserial PRIMARY KEY,
                                         task_id bigint NOT NULL,
                                         step_order integer NOT NULL,
                                         step_name varchar(100) NOT NULL,
    step_description varchar(500),
    status varchar(20) NOT NULL DEFAULT 'PENDING',
    api_endpoint varchar(500) NOT NULL,
    http_method varchar(10) NOT NULL,
    request_headers text,
    request_params text,
    request_body text,
    timeout_seconds integer DEFAULT 30,
    retry_count integer DEFAULT 0,
    max_retries integer DEFAULT 3,
    required varchar(1) DEFAULT '1',
    continue_on_failure varchar(1) DEFAULT '0',
    start_time timestamp(6),
    end_time timestamp(6),
    execution_duration bigint,
    response_status integer,
    response_data text,
    error_message text,
    output_data text,
    create_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by varchar(64) NOT NULL,
    CONSTRAINT fk_task_step_task_id FOREIGN KEY (task_id) REFERENCES workflow_task(task_id) ON DELETE CASCADE
    );

-- 添加表注释
COMMENT ON TABLE task_step IS '任务步骤表';
COMMENT ON COLUMN task_step.step_id IS '步骤ID';
COMMENT ON COLUMN task_step.task_id IS '任务ID';
COMMENT ON COLUMN task_step.step_order IS '步骤序号';
COMMENT ON COLUMN task_step.step_name IS '步骤名称';
COMMENT ON COLUMN task_step.step_description IS '步骤描述';
COMMENT ON COLUMN task_step.status IS '步骤状态：PENDING-待执行, RUNNING-执行中, COMPLETED-已完成, FAILED-失败, SKIPPED-已跳过';
COMMENT ON COLUMN task_step.api_endpoint IS 'API端点URL';
COMMENT ON COLUMN task_step.http_method IS 'HTTP方法：GET, POST, PUT, DELETE';
COMMENT ON COLUMN task_step.request_headers IS '请求头配置JSON';
COMMENT ON COLUMN task_step.request_params IS '请求参数配置JSON';
COMMENT ON COLUMN task_step.request_body IS '请求体配置JSON';
COMMENT ON COLUMN task_step.timeout_seconds IS '超时时间（秒）';
COMMENT ON COLUMN task_step.retry_count IS '重试次数';
COMMENT ON COLUMN task_step.max_retries IS '最大重试次数';
COMMENT ON COLUMN task_step.required IS '是否必须成功：0-否, 1-是';
COMMENT ON COLUMN task_step.continue_on_failure IS '失败时是否继续：0-否, 1-是';
COMMENT ON COLUMN task_step.start_time IS '开始时间';
COMMENT ON COLUMN task_step.end_time IS '结束时间';
COMMENT ON COLUMN task_step.execution_duration IS '执行时长（毫秒）';
COMMENT ON COLUMN task_step.response_status IS '响应状态码';
COMMENT ON COLUMN task_step.response_data IS '响应数据';
COMMENT ON COLUMN task_step.error_message IS '错误信息';
COMMENT ON COLUMN task_step.output_data IS '输出数据（传递给下一步的数据）';
COMMENT ON COLUMN task_step.create_time IS '创建时间';
COMMENT ON COLUMN task_step.create_by IS '创建者';

-- 3. 任务执行记录表
CREATE TABLE IF NOT EXISTS task_execution (
                                              execution_id bigserial PRIMARY KEY,
                                              task_id bigint NOT NULL,
                                              execution_batch varchar(64),
    status varchar(20) NOT NULL DEFAULT 'STARTED',
    start_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_time timestamp(6),
    execution_duration bigint,
    current_step integer DEFAULT 0,
    total_steps integer DEFAULT 0,
    progress_percentage integer DEFAULT 0,
    success_steps integer DEFAULT 0,
    failed_steps integer DEFAULT 0,
    skipped_steps integer DEFAULT 0,
    error_message text,
    execution_log text,
    execution_result text,
    execution_environment text,
    executor_id bigint,
    executor_name varchar(64),
    manually_terminated varchar(1) DEFAULT '0',
    termination_reason varchar(500),
    retry_count integer DEFAULT 0,
    parent_execution_id bigint,
    CONSTRAINT fk_task_execution_task_id FOREIGN KEY (task_id) REFERENCES workflow_task(task_id) ON DELETE CASCADE,
    CONSTRAINT fk_task_execution_parent FOREIGN KEY (parent_execution_id) REFERENCES task_execution(execution_id)
    );

-- 创建索引
-- 索引创建
CREATE INDEX IF NOT EXISTS idx_workflow_task_status ON workflow_task(status);
CREATE INDEX IF NOT EXISTS idx_workflow_task_type ON workflow_task(task_type);
CREATE INDEX IF NOT EXISTS idx_workflow_task_priority ON workflow_task(priority);
CREATE INDEX IF NOT EXISTS idx_workflow_task_create_time ON workflow_task(create_time);
CREATE INDEX IF NOT EXISTS idx_task_step_task_id ON task_step(task_id);
CREATE INDEX IF NOT EXISTS idx_task_step_status ON task_step(status);
CREATE INDEX IF NOT EXISTS idx_task_step_order ON task_step(task_id, step_order);
CREATE INDEX IF NOT EXISTS idx_task_execution_task_id ON task_execution(task_id);
CREATE INDEX IF NOT EXISTS idx_task_execution_status ON task_execution(status);
CREATE INDEX IF NOT EXISTS idx_task_execution_batch ON task_execution(execution_batch);
CREATE INDEX IF NOT EXISTS idx_task_execution_start_time ON task_execution(start_time);

-- 添加表注释
COMMENT ON TABLE task_execution IS '任务执行记录表';
COMMENT ON COLUMN task_execution.execution_id IS '执行记录ID';
COMMENT ON COLUMN task_execution.task_id IS '任务ID';
COMMENT ON COLUMN task_execution.execution_batch IS '执行批次号';
COMMENT ON COLUMN task_execution.status IS '执行状态：STARTED-已开始, RUNNING-执行中, COMPLETED-已完成, FAILED-失败, TERMINATED-已终止';
COMMENT ON COLUMN task_execution.start_time IS '开始时间';
COMMENT ON COLUMN task_execution.end_time IS '结束时间';
COMMENT ON COLUMN task_execution.execution_duration IS '执行时长（毫秒）';
COMMENT ON COLUMN task_execution.current_step IS '当前执行步骤';
COMMENT ON COLUMN task_execution.total_steps IS '总步骤数';
COMMENT ON COLUMN task_execution.progress_percentage IS '执行进度百分比';
COMMENT ON COLUMN task_execution.success_steps IS '成功步骤数';
COMMENT ON COLUMN task_execution.failed_steps IS '失败步骤数';
COMMENT ON COLUMN task_execution.skipped_steps IS '跳过步骤数';
COMMENT ON COLUMN task_execution.error_message IS '错误信息';
COMMENT ON COLUMN task_execution.execution_log IS '执行日志';
COMMENT ON COLUMN task_execution.execution_result IS '执行结果';
COMMENT ON COLUMN task_execution.execution_environment IS '执行环境信息';
COMMENT ON COLUMN task_execution.executor_id IS '执行者ID';
COMMENT ON COLUMN task_execution.executor_name IS '执行者名称';
COMMENT ON COLUMN task_execution.manually_terminated IS '是否手动终止：0-否, 1-是';
COMMENT ON COLUMN task_execution.termination_reason IS '终止原因';
COMMENT ON COLUMN task_execution.retry_count IS '重试次数';
COMMENT ON COLUMN task_execution.parent_execution_id IS '父执行记录ID（重试时关联原执行记录）';

CREATE TABLE IF NOT EXISTS api_integrated_product
(
    id                BIGSERIAL
        CONSTRAINT api_integrated_product
            PRIMARY KEY,
    release_id        BIGINT       NOT NULL,
    integration_name  VARCHAR(100) NOT NULL,
    capability        VARCHAR(255) NOT NULL,
    product_name      VARCHAR(100) NOT NULL,
    company           VARCHAR(255) NOT NULL,
    logo              TEXT,
    status            VARCHAR(50)  NOT NULL DEFAULT 'NORMAL_CONNECTION',
    config_params     TEXT,
    auth_params       TEXT,
    error_message     TEXT,
    version           INTEGER,
    create_by         VARCHAR(50)  NOT NULL,
    create_time       TIMESTAMP(6) NOT NULL,
    update_by         VARCHAR(50),
    update_time       TIMESTAMP(6)
);

-- 添加表注释
COMMENT ON TABLE api_integrated_product IS '集成产品表，用于存储已集成的第三方产品信息及其状态';

-- 添加字段注释
COMMENT ON COLUMN api_integrated_product.id IS '主键ID';
COMMENT ON COLUMN api_integrated_product.release_id IS '关联API版本记录ID';
COMMENT ON COLUMN api_integrated_product.integration_name IS '集成名称';
COMMENT ON COLUMN api_integrated_product.capability IS '产品能力';
COMMENT ON COLUMN api_integrated_product.product_name IS '产品类型名称';
COMMENT ON COLUMN api_integrated_product.company IS '所属公司';
COMMENT ON COLUMN api_integrated_product.logo IS '产品logo';
COMMENT ON COLUMN api_integrated_product.status IS '集成状态：NORMAL_CONNECTION-正常连接，CONNECTION_ERROR-连接错误，PARAMETER_UPDATED-参数已更新，DELETED_FROM_KNOWLEDGE_BASE-已从知识库删除';
COMMENT ON COLUMN api_integrated_product.config_params IS '配置参数（JSON格式）';
COMMENT ON COLUMN api_integrated_product.auth_params IS '授权参数（JSON格式）';
COMMENT ON COLUMN api_integrated_product.error_message IS '错误信息';
COMMENT ON COLUMN api_integrated_product.version IS '版本号';
COMMENT ON COLUMN api_integrated_product.create_by IS '创建人';
COMMENT ON COLUMN api_integrated_product.create_time IS '创建时间';
COMMENT ON COLUMN api_integrated_product.update_by IS '修改人';
COMMENT ON COLUMN api_integrated_product.update_time IS '修改时间';