# getParamsByCapability 方法实现说明

## 功能概述

`getParamsByCapability` 方法根据页面上选择的产品类型ID（api_release表的id）查询api_interface表中所有接口的req_params字段，将其中保存的InParam对象列表转化为页面上展示的表单字段格式。

## 实现架构

### 1. 数据流程

```
产品类型ID (releaseId) 
    ↓
查询 api_interface 表 (根据 release_id)
    ↓
解析 req_params 字段 (JSON → InParam对象)
    ↓
转换为 IntegrationFormFieldVO 列表
    ↓
返回给前端用于动态表单渲染
```

### 2. 核心组件

#### 2.1 实体类
- **InParam**: 存储接口参数的原始结构
- **InParam.ColumnParam**: 单个参数的详细信息
- **IntegrationFormFieldVO**: 前端表单字段配置

#### 2.2 服务层
- **IApiReleaseService.getParamsByCapability()**: 主要业务方法
- **IApiInterfaceService.getByReleaseId()**: 查询接口数据

#### 2.3 控制器
- **ApiProductIntegrationController.getParamsByCapability()**: API端点

## 数据结构说明

### 输入数据格式 (api_interface.req_params)

```json
{
    "columnList": [
        {
            "columnName": "target_url",
            "columnComment": "目标URL",
            "type": 1,
            "value": ""
        },
        {
            "columnName": "username", 
            "columnComment": "用户名",
            "type": 1,
            "value": ""
        }
    ]
}
```

### 输出数据格式 (IntegrationFormFieldVO)

```json
[
    {
        "fieldName": "target_url",
        "fieldLabel": "目标URL",
        "fieldType": "url",
        "required": true,
        "placeholder": "请输入API地址，如：https://api.example.com",
        "defaultValue": "",
        "description": "来自接口: 启动扫描任务 - 目标URL",
        "sourceType": 1,
        "validationRules": [
            {
                "type": "required",
                "value": "true",
                "message": "目标URL不能为空"
            },
            {
                "type": "url", 
                "value": "true",
                "message": "请输入有效的URL地址"
            }
        ]
    }
]
```

## 字段类型推断规则

### 自动推断逻辑

| 字段名称包含 | 字段注释包含 | 推断类型 | 说明 |
|-------------|-------------|----------|------|
| url, endpoint, host | 地址, url | url | URL输入框 |
| password, secret, token | 密码, 令牌, 秘钥 | password | 密码框 |
| username, user, account | 用户, 账号 | input | 普通输入框 |
| desc, comment, remark | 描述, 备注, 说明 | textarea | 文本域 |
| 其他 | 其他 | input | 默认输入框 |

### 验证规则生成

1. **必填验证**: 当 `type = 1` (终端输入) 时自动添加
2. **URL验证**: 当字段类型为 `url` 时自动添加
3. **扩展验证**: 可根据业务需求添加更多规则

## API使用示例

### 请求
```http
GET /api/product/integration/products/1
```

### 响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "fieldName": "target_url",
            "fieldLabel": "目标URL",
            "fieldType": "url",
            "required": true,
            "placeholder": "请输入API地址，如：https://api.example.com",
            "defaultValue": "",
            "description": "来自接口: 启动扫描任务 - 目标URL",
            "sourceType": 1,
            "validationRules": [
                {
                    "type": "required",
                    "value": "true",
                    "message": "目标URL不能为空"
                }
            ]
        }
    ]
}
```

## 前端集成示例

### Vue.js 动态表单渲染

```vue
<template>
  <el-form :model="formData" :rules="formRules" ref="integrationForm">
    <el-form-item 
      v-for="field in formFields" 
      :key="field.fieldName"
      :label="field.fieldLabel"
      :prop="field.fieldName"
      :required="field.required"
    >
      <!-- URL输入框 -->
      <el-input 
        v-if="field.fieldType === 'url'"
        v-model="formData[field.fieldName]"
        :placeholder="field.placeholder"
        type="url"
      />
      
      <!-- 密码输入框 -->
      <el-input 
        v-else-if="field.fieldType === 'password'"
        v-model="formData[field.fieldName]"
        :placeholder="field.placeholder"
        type="password"
        show-password
      />
      
      <!-- 文本域 -->
      <el-input 
        v-else-if="field.fieldType === 'textarea'"
        v-model="formData[field.fieldName]"
        :placeholder="field.placeholder"
        type="textarea"
        :rows="3"
      />
      
      <!-- 普通输入框 -->
      <el-input 
        v-else
        v-model="formData[field.fieldName]"
        :placeholder="field.placeholder"
      />
      
      <!-- 字段描述 -->
      <div class="field-description" v-if="field.description">
        {{ field.description }}
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  data() {
    return {
      formFields: [],
      formData: {},
      formRules: {}
    }
  },
  methods: {
    async loadFormFields(productId) {
      try {
        const response = await this.$http.get(`/api/product/integration/products/${productId}`);
        this.formFields = response.data;
        this.generateFormRules();
        this.initFormData();
      } catch (error) {
        this.$message.error('加载表单配置失败');
      }
    },
    
    generateFormRules() {
      const rules = {};
      this.formFields.forEach(field => {
        if (field.validationRules && field.validationRules.length > 0) {
          rules[field.fieldName] = field.validationRules.map(rule => ({
            required: rule.type === 'required',
            type: rule.type === 'url' ? 'url' : undefined,
            message: rule.message,
            trigger: 'blur'
          }));
        }
      });
      this.formRules = rules;
    },
    
    initFormData() {
      const data = {};
      this.formFields.forEach(field => {
        data[field.fieldName] = field.defaultValue || '';
      });
      this.formData = data;
    }
  }
}
</script>
```

## 测试验证

### 单元测试
运行 `ApiReleaseServiceTest.java` 中的测试用例：

```bash
mvn test -Dtest=ApiReleaseServiceTest
```

### 手动测试
1. 启动应用
2. 访问: `GET /api/product/integration/products/1`
3. 验证返回的JSON格式是否正确

## 扩展功能

### 1. 添加新的字段类型
在 `inferFieldType` 方法中添加新的推断规则：

```java
// 数字字段
if (lowerName.contains("port") || lowerName.contains("count")) {
    return "number";
}

// 日期字段  
if (lowerName.contains("date") || lowerName.contains("time")) {
    return "date";
}
```

### 2. 自定义验证规则
在 `generateValidationRules` 方法中添加新的验证逻辑：

```java
// 端口号验证
if (lowerName.contains("port")) {
    IntegrationFormFieldVO.ValidationRule portRule = new IntegrationFormFieldVO.ValidationRule();
    portRule.setType("range");
    portRule.setValue("1-65535");
    portRule.setMessage("端口号必须在1-65535之间");
    rules.add(portRule);
}
```

### 3. 字段选项配置
为select类型字段添加预定义选项：

```java
private List<IntegrationFormFieldVO.FieldOption> generateFieldOptions(String columnName) {
    List<IntegrationFormFieldVO.FieldOption> options = new ArrayList<>();
    
    if ("scan_type".equals(columnName)) {
        options.add(createOption("快速扫描", "quick"));
        options.add(createOption("深度扫描", "deep"));
        options.add(createOption("自定义扫描", "custom"));
    }
    
    return options;
}
```

## 注意事项

1. **JSON解析异常**: 确保api_interface表中的req_params字段是有效的JSON格式
2. **字段去重**: 多个接口可能有相同的参数名，系统会自动去重
3. **性能考虑**: 对于大量接口的产品，考虑添加缓存机制
4. **数据验证**: 前端应该对用户输入进行二次验证
5. **国际化**: 字段标签和错误信息支持多语言扩展

## 故障排除

### 常见问题

1. **返回空列表**: 检查releaseId是否存在，api_interface表是否有对应数据
2. **JSON解析失败**: 检查req_params字段的JSON格式是否正确
3. **字段类型错误**: 检查字段名称和注释是否符合推断规则
4. **验证规则不生效**: 检查前端是否正确处理validationRules数组

### 调试方法

1. 启用DEBUG日志查看详细执行过程
2. 使用测试用例验证核心逻辑
3. 通过API直接测试返回结果
4. 检查数据库中的原始数据格式
