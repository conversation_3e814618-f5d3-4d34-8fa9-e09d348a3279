package com.dcas.system.service.impl;

import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.system.service.IApiInterfaceService;
import com.dcas.system.service.IApiReleaseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ApiReleaseService测试类
 * 测试getParamsByCapability方法的功能
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class ApiReleaseServiceTest {

    @Autowired
    private IApiReleaseService apiReleaseService;

    @Autowired
    private IApiInterfaceService apiInterfaceService;

    /**
     * 测试getParamsByCapability方法
     * 验证能否正确解析API接口参数并转换为前端表单字段格式
     */
    @Test
    public void testGetParamsByCapability() {
        // 准备测试数据
        Long releaseId = 1L;

        // 执行方法
        List<IntegrationFormFieldVO> formFields = apiReleaseService.getParamsByCapability(releaseId);

        // 验证结果
        assertNotNull(formFields, "返回的表单字段列表不应为null");
        
        if (!formFields.isEmpty()) {
            // 验证字段结构
            IntegrationFormFieldVO firstField = formFields.get(0);
            assertNotNull(firstField.getFieldName(), "字段名称不应为null");
            assertNotNull(firstField.getFieldLabel(), "字段标签不应为null");
            assertNotNull(firstField.getFieldType(), "字段类型不应为null");
            assertNotNull(firstField.getRequired(), "必填标识不应为null");
            
            // 打印结果用于调试
            System.out.println("=== 解析到的表单字段 ===");
            for (IntegrationFormFieldVO field : formFields) {
                System.out.println(String.format(
                    "字段名: %s, 标签: %s, 类型: %s, 必填: %s, 占位符: %s",
                    field.getFieldName(),
                    field.getFieldLabel(),
                    field.getFieldType(),
                    field.getRequired(),
                    field.getPlaceholder()
                ));
                
                // 验证验证规则
                if (field.getValidationRules() != null) {
                    for (IntegrationFormFieldVO.ValidationRule rule : field.getValidationRules()) {
                        System.out.println(String.format(
                            "  验证规则: %s = %s, 错误信息: %s",
                            rule.getType(),
                            rule.getValue(),
                            rule.getMessage()
                        ));
                    }
                }
            }
        }
    }

    /**
     * 测试字段类型推断功能
     */
    @Test
    public void testFieldTypeInference() {
        Long releaseId = 1L;
        List<IntegrationFormFieldVO> formFields = apiReleaseService.getParamsByCapability(releaseId);

        for (IntegrationFormFieldVO field : formFields) {
            String fieldName = field.getFieldName().toLowerCase();
            String fieldType = field.getFieldType();

            // 验证URL字段类型推断
            if (fieldName.contains("url") || fieldName.contains("endpoint")) {
                assertEquals("url", fieldType, "URL相关字段应该被识别为url类型");
            }

            // 验证密码字段类型推断
            if (fieldName.contains("password") || fieldName.contains("token")) {
                assertEquals("password", fieldType, "密码相关字段应该被识别为password类型");
            }

            // 验证描述字段类型推断
            if (fieldName.contains("desc") || fieldName.contains("comment")) {
                assertEquals("textarea", fieldType, "描述相关字段应该被识别为textarea类型");
            }
        }
    }

    /**
     * 测试验证规则生成功能
     */
    @Test
    public void testValidationRules() {
        Long releaseId = 1L;
        List<IntegrationFormFieldVO> formFields = apiReleaseService.getParamsByCapability(releaseId);

        for (IntegrationFormFieldVO field : formFields) {
            if (field.getRequired() != null && field.getRequired()) {
                // 必填字段应该有required验证规则
                boolean hasRequiredRule = field.getValidationRules().stream()
                        .anyMatch(rule -> "required".equals(rule.getType()));
                assertTrue(hasRequiredRule, "必填字段应该包含required验证规则");
            }

            if ("url".equals(field.getFieldType())) {
                // URL字段应该有url验证规则
                boolean hasUrlRule = field.getValidationRules().stream()
                        .anyMatch(rule -> "url".equals(rule.getType()));
                assertTrue(hasUrlRule, "URL字段应该包含url验证规则");
            }
        }
    }

    /**
     * 测试空数据处理
     */
    @Test
    public void testEmptyData() {
        // 测试不存在的releaseId
        Long nonExistentReleaseId = 99999L;
        List<IntegrationFormFieldVO> formFields = apiReleaseService.getParamsByCapability(nonExistentReleaseId);
        
        assertNotNull(formFields, "即使没有数据，也应该返回空列表而不是null");
        assertTrue(formFields.isEmpty(), "不存在的releaseId应该返回空列表");
    }

    /**
     * 测试字段去重功能
     */
    @Test
    public void testFieldDeduplication() {
        Long releaseId = 1L;
        List<IntegrationFormFieldVO> formFields = apiReleaseService.getParamsByCapability(releaseId);

        // 检查是否有重复的字段名
        long uniqueFieldCount = formFields.stream()
                .map(IntegrationFormFieldVO::getFieldName)
                .distinct()
                .count();

        assertEquals(formFields.size(), uniqueFieldCount, "应该没有重复的字段名");
    }

    /**
     * 手动测试方法 - 用于开发调试
     * 可以通过修改releaseId来测试不同的产品配置
     */
    public static void main(String[] args) {
        // 这个方法可以在开发环境中手动运行来测试功能
        System.out.println("请在Spring Boot测试环境中运行测试用例");
        System.out.println("示例API调用: GET /api/product/integration/products/1");
        System.out.println("预期返回: 包含字段配置的JSON数组");
    }
}
