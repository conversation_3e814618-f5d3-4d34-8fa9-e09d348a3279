-- 创建集成产品表
CREATE TABLE integrated_product
(
    id                BIGSERIAL
        CONSTRAINT integrated_product_pk
            PRIMARY KEY,
    release_id        BIGINT       NOT NULL,
    integration_name  VARCHAR(100) NOT NULL,
    capability        VARCHAR(255) NOT NULL,
    product_name      VARCHAR(100) NOT NULL,
    company           VARCHAR(255) NOT NULL,
    logo              TEXT,
    status            VARCHAR(50)  NOT NULL DEFAULT 'NORMAL_CONNECTION',
    config_params     TEXT,
    auth_params       TEXT,
    last_sync_time    TIMESTAMP(6),
    error_message     TEXT,
    enabled           BOOLEAN      NOT NULL DEFAULT TRUE,
    create_by         VARCHAR(50)  NOT NULL,
    create_time       TIMESTAMP(6) NOT NULL,
    update_by         VARCHAR(50),
    update_time       TIMESTAMP(6),
    remark            VARCHAR(500)
);

-- 添加表注释
COMMENT ON TABLE integrated_product IS '集成产品表，用于存储已集成的第三方产品信息及其状态';

-- 添加字段注释
COMMENT ON COLUMN integrated_product.id IS '主键ID';
COMMENT ON COLUMN integrated_product.release_id IS '关联API版本记录ID';
COMMENT ON COLUMN integrated_product.integration_name IS '集成名称';
COMMENT ON COLUMN integrated_product.capability IS '产品能力';
COMMENT ON COLUMN integrated_product.product_name IS '产品类型名称';
COMMENT ON COLUMN integrated_product.company IS '所属公司';
COMMENT ON COLUMN integrated_product.logo IS '产品logo';
COMMENT ON COLUMN integrated_product.status IS '集成状态：NORMAL_CONNECTION-正常连接，CONNECTION_ERROR-连接错误，PARAMETER_UPDATED-参数已更新，DELETED_FROM_KNOWLEDGE_BASE-已从知识库删除';
COMMENT ON COLUMN integrated_product.config_params IS '配置参数（JSON格式）';
COMMENT ON COLUMN integrated_product.auth_params IS '授权参数（JSON格式）';
COMMENT ON COLUMN integrated_product.last_sync_time IS '最后同步时间';
COMMENT ON COLUMN integrated_product.error_message IS '错误信息';
COMMENT ON COLUMN integrated_product.enabled IS '是否启用';
COMMENT ON COLUMN integrated_product.create_by IS '创建人';
COMMENT ON COLUMN integrated_product.create_time IS '创建时间';
COMMENT ON COLUMN integrated_product.update_by IS '修改人';
COMMENT ON COLUMN integrated_product.update_time IS '修改时间';
COMMENT ON COLUMN integrated_product.remark IS '备注';

-- 创建索引
CREATE INDEX idx_integrated_product_release_id ON integrated_product (release_id);
CREATE INDEX idx_integrated_product_capability ON integrated_product (capability);
CREATE INDEX idx_integrated_product_status ON integrated_product (status);
CREATE INDEX idx_integrated_product_enabled ON integrated_product (enabled);
CREATE UNIQUE INDEX uk_integrated_product_name ON integrated_product (integration_name);

-- 添加外键约束（如果需要的话）
-- ALTER TABLE integrated_product ADD CONSTRAINT fk_integrated_product_release_id 
--     FOREIGN KEY (release_id) REFERENCES api_release (id);

-- 插入示例数据（可选）
INSERT INTO integrated_product (
    release_id, integration_name, capability, product_name, company, logo, 
    status, config_params, auth_params, last_sync_time, enabled, 
    create_by, create_time, remark
) VALUES 
(1, '示例安全扫描集成', '安全扫描', '示例扫描器', '示例公司', 'http://example.com/logo.png', 
 'NORMAL_CONNECTION', '{"host":"*************","port":8080}', '{"token":"example_token"}', 
 NOW(), TRUE, 'admin', NOW(), '示例集成配置'),
(2, '示例漏洞检测集成', '漏洞检测', '示例检测器', '示例公司2', 'http://example2.com/logo.png', 
 'NORMAL_CONNECTION', '{"endpoint":"https://api.example2.com"}', '{"apiKey":"example_api_key"}', 
 NOW(), TRUE, 'admin', NOW(), '示例漏洞检测集成');
