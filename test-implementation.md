# getProductsByCapability 方法实现完成

## 实现概述

我已经成功完成了 `ApiReleaseServiceImpl.getProductsByCapability(Long id)` 方法的实现。该方法的主要功能是：

1. **根据产品类型ID查询接口数据**：通过 `apiInterfaceService.getByReleaseId(id)` 查询 `api_interface` 表中 `release_id` 等于传入ID的所有接口数据。

2. **解析req_params字段**：将每个接口的 `req_params` 字段（JSON格式）解析为 `InParam` 对象。

3. **筛选type=1的参数**：遍历 `InParam.ColumnParam` 列表，只收集 `type=1` 的参数（终端输入参数）。

4. **去重处理**：如果不同接口中存在同名参数，保留序号大的接口中的参数（根据 `sort` 字段比较）。

5. **转换为表单字段**：将筛选出的参数转换为 `IntegrationFormFieldVO` 对象，用于前端动态表单渲染。

## 核心实现逻辑

### 1. 数据查询
```java
List<ApiInterface> interfaces = apiInterfaceService.getByReleaseId(id);
```

### 2. JSON解析
```java
InParam inParam = JSONUtil.toBean(reqParams, InParam.class);
```

### 3. 参数筛选
```java
if (columnParam.getType() != null && columnParam.getType() == 1) {
    // 处理终端输入参数
}
```

### 4. 去重逻辑
```java
if (existingField == null || shouldReplaceField(apiInterface, existingField.getInterfaceId(), interfaces)) {
    // 保留序号大的接口中的参数
}
```

### 5. 字段转换
```java
IntegrationFormFieldVO fieldVO = convertToFormField(columnParam, apiInterface);
```

## 关键特性

1. **智能字段类型推断**：根据字段名自动推断字段类型（url、password、email、number、textarea等）
2. **异常处理**：完善的异常处理和日志记录
3. **去重策略**：基于接口序号的智能去重
4. **扩展性**：易于扩展新的字段类型和验证规则

## 修改的文件

1. **InParam.java**：将 `ColumnParam` 类从 `private` 改为 `public`，使其可以被外部访问
2. **IApiReleaseService.java**：更新方法签名，返回 `List<IntegrationFormFieldVO>`
3. **ApiReleaseServiceImpl.java**：完整实现 `getProductsByCapability` 方法及相关辅助方法

## 方法签名

```java
@Override
public List<IntegrationFormFieldVO> getProductsByCapability(Long id)
```

## 返回数据结构

每个 `IntegrationFormFieldVO` 包含：
- `interfaceId`：所属接口ID
- `name`：字段名称
- `label`：字段标签（优先使用注释，否则使用字段名）
- `type`：字段类型（根据字段名智能推断）
- `value`：字段值
- `required`：是否必填（type=1的参数默认为必填）

## 测试建议

建议创建单元测试验证以下场景：
1. 正常数据解析
2. 空数据处理
3. 字段去重功能
4. 字段类型推断
5. 异常情况处理

实现已完成，可以直接使用。
